"""
多模态图像检索模块
使用CLIP和多模态大模型对PDF文档中的图像进行智能检索和分析
"""
import os
import sys
import json
import base64
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from loguru import logger
import numpy as np
from PIL import Image
import io

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    import torch
    import clip
    from transformers import BlipProcessor, BlipForConditionalGeneration
    from transformers import CLIPProcessor, CLIPModel
    import fitz  # PyMuPDF
    import cv2
    MULTIMODAL_AVAILABLE = True
except ImportError:
    MULTIMODAL_AVAILABLE = False
    logger.warning("多模态功能需要安装: torch, transformers, clip-by-openai, pymupdf, opencv-python, pillow")

from rag.rag_system import RAGSystem
from rag.colorspace_utils import ColorSpaceHandler
from rag.image_quality_enhancer import ImageQualityEnhancer
from idconfig.config import Config
from llm.llm_service import LLMService

class MultimodalImageRetriever:
    """多模态图像检索器"""
    
    def __init__(self):
        self.config = Config()
        self.rag_system = RAGSystem()
        self.llm_service = LLMService()
        
        # 图像索引存储
        self.image_index_file = Path("data/image_index.json")
        self.image_index_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 图像存储目录
        self.image_storage_dir = Path("data/images")
        self.image_storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载图像索引
        self.image_index = self._load_image_index()
        
        # 模型初始化标志
        self.clip_model = None
        self.clip_processor = None
        self.blip_model = None
        self.blip_processor = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 图像质量增强器
        self.quality_enhancer = ImageQualityEnhancer()
        
        # 金融图像关键词
        self.financial_image_keywords = {
            "图表类型": {
                "柱状图": ["bar chart", "column chart", "柱状图", "条形图"],
                "折线图": ["line chart", "line graph", "折线图", "趋势图"],
                "饼图": ["pie chart", "饼图", "圆饼图"],
                "散点图": ["scatter plot", "散点图", "分布图"],
                "K线图": ["candlestick", "k-line", "K线图", "蜡烛图"]
            },
            "财务内容": {
                "财务报表": ["financial statement", "balance sheet", "财务报表", "资产负债表"],
                "收入图表": ["revenue chart", "income chart", "营收图", "收入图"],
                "利润分析": ["profit analysis", "利润分析", "盈利图"],
                "现金流": ["cash flow", "现金流", "资金流"],
                "市场数据": ["market data", "股价", "交易量", "市值"]
            },
            "业务指标": {
                "增长趋势": ["growth trend", "增长趋势", "发展趋势"],
                "市场份额": ["market share", "市场份额", "占有率"],
                "客户分析": ["customer analysis", "客户分析", "用户画像"],
                "产品分布": ["product distribution", "产品分布", "业务结构"]
            }
        }
    
    def initialize(self):
        """初始化多模态检索器"""
        try:
            if not MULTIMODAL_AVAILABLE:
                logger.error("多模态功能不可用，请安装必要的依赖包")
                return False
            
            # 初始化RAG系统
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
            
            # 初始化CLIP模型
            logger.info("初始化CLIP模型...")
            self._initialize_clip_model()
            
            # 初始化BLIP模型（用于图像描述生成）
            logger.info("初始化BLIP模型...")
            self._initialize_blip_model()
            
            logger.info("多模态图像检索器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"多模态检索器初始化失败: {e}")
            return False
    
    def _initialize_clip_model(self):
        """初始化CLIP模型"""
        try:
            # 使用OpenAI的CLIP模型
            self.clip_model, self.clip_preprocess = clip.load("ViT-B/32", device=self.device)
            logger.info("CLIP模型加载成功")
        except Exception as e:
            logger.warning(f"CLIP模型加载失败，尝试使用HuggingFace版本: {e}")
            try:
                # 备用方案：使用HuggingFace的CLIP
                self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
                self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
                self.clip_model.to(self.device)
                logger.info("HuggingFace CLIP模型加载成功")
            except Exception as e2:
                logger.error(f"所有CLIP模型加载失败: {e2}")
                self.clip_model = None
    
    def _initialize_blip_model(self):
        """初始化BLIP模型"""
        try:
            self.blip_processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
            self.blip_model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")
            self.blip_model.to(self.device)
            logger.info("BLIP模型加载成功")
        except Exception as e:
            logger.warning(f"BLIP模型加载失败: {e}")
            self.blip_model = None
    
    def _load_image_index(self) -> Dict[str, Any]:
        """加载图像索引"""
        try:
            if self.image_index_file.exists():
                with open(self.image_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载图像索引失败: {e}")
            return {}
    
    def _convert_numpy_types(self, obj):
        """递归转换numpy类型为Python原生类型"""
        if isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.bool_, np.bool8)):
            return bool(obj)
        elif isinstance(obj, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, np.str_):
            return str(obj)
        else:
            return obj

    def _save_image_index(self):
        """保存图像索引"""
        try:
            # 转换numpy类型为JSON可序列化的类型
            serializable_index = self._convert_numpy_types(self.image_index)

            with open(self.image_index_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存图像索引失败: {e}")

    def _image_to_base64(self, image: Image.Image) -> str:
        """将PIL图像转换为base64编码"""
        try:
            # 将图像转换为RGB格式（如果不是的话）
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 保存到内存中的字节流
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            buffer.seek(0)

            # 转换为base64
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            return image_base64

        except Exception as e:
            logger.error(f"图像转base64失败: {e}")
            return ""

    def extract_images_from_pdf(self, pdf_path: str, file_hash: str) -> List[Dict[str, Any]]:
        """从PDF中提取图像"""
        try:
            if not MULTIMODAL_AVAILABLE:
                logger.warning("多模态功能不可用")
                return []
            
            images_info = []
            doc = fitz.open(pdf_path)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        # 提取图像数据
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        # 使用安全的颜色空间转换工具
                        pil_image = ColorSpaceHandler.safe_pixmap_to_pil(pix)

                        if pil_image is None:
                            logger.warning(f"第{page_num+1}页第{img_index+1}个图像转换失败")
                            pix = None
                            continue

                        # 验证图像有效性
                        is_valid, validation_msg = ColorSpaceHandler.validate_image(pil_image)
                        if not is_valid:
                            logger.warning(f"图像验证失败: {validation_msg}")
                            pix = None
                            continue
                        
                        # 过滤小图像（可能是装饰性图像）
                        if pil_image.width < 100 or pil_image.height < 100:
                            continue

                        # 图像质量检查和过滤
                        quality_passed, quality_info = self.quality_enhancer.filter_low_quality_images(pil_image)
                        if not quality_passed:
                            logger.info(f"第{page_num+1}页第{img_index+1}个图像质量不达标，跳过处理")
                            continue

                        # 图像质量增强
                        enhanced_image, enhancement_info = self.quality_enhancer.enhance_image(pil_image)

                        # 生成图像ID和保存路径
                        image_id = f"{file_hash}_p{page_num+1}_i{img_index+1}"
                        image_filename = f"{image_id}.png"
                        image_path = self.image_storage_dir / image_filename

                        # 保存增强后的图像
                        enhanced_image.save(image_path, "PNG")
                        
                        # 使用增强后的图像进行分析
                        # 提取页面上下文信息（如果可能）
                        context_info = None
                        try:
                            # 尝试从PDF处理器获取上下文信息
                            if hasattr(self, '_extract_page_context_text'):
                                context_info = self._extract_page_context_text(pdf_path, page_num)
                        except:
                            pass

                        # 生成图像描述（使用上下文信息）
                        description = self._generate_image_description(enhanced_image, context_info)

                        # 生成图像嵌入向量
                        embedding = self._generate_image_embedding(enhanced_image)

                        # 分类图像类型（结合质量增强器的检测）
                        enhanced_type = self.quality_enhancer.detect_image_type(enhanced_image)
                        image_type = self._classify_image_type(description, enhanced_image, enhanced_type)
                        
                        image_info = {
                            "image_id": image_id,
                            "file_hash": file_hash,
                            "page_number": page_num + 1,
                            "image_index": img_index + 1,
                            "image_path": str(image_path),
                            "width": enhanced_image.width,
                            "height": enhanced_image.height,
                            "description": description,
                            "image_type": image_type,
                            "embedding": embedding.tolist() if embedding is not None else None,
                            "extracted_text": self._extract_text_from_image(enhanced_image),
                            "quality_info": quality_info,
                            "enhancement_info": enhancement_info,
                            "original_size": (pil_image.width, pil_image.height)
                        }
                        
                        images_info.append(image_info)
                        pix = None  # 释放内存
                        
                    except Exception as e:
                        logger.warning(f"处理第{page_num+1}页第{img_index+1}个图像失败: {e}")
                        continue
            
            doc.close()
            logger.info(f"从PDF提取到 {len(images_info)} 个图像")
            return images_info
            
        except Exception as e:
            logger.error(f"从PDF提取图像失败: {e}")
            return []
    
    def _generate_image_description(self, image: Image.Image, context_info: Dict[str, str] = None) -> str:
        """
        生成图像描述（支持上下文增强）

        Args:
            image: PIL图像对象
            context_info: 上下文信息字典，包含页面文本等
        """
        try:
            # 优先使用硅基流动2的多模态API
            if self.config.SILICONFLOW2_API_KEY:
                try:
                    # 将图像转换为base64
                    image_base64 = self._image_to_base64(image)
                    if image_base64:
                        # 构建包含上下文信息的提示
                        prompt = self._build_context_enhanced_prompt(context_info)

                        description = self.llm_service.call_siliconflow2_vision(
                            image_base64=image_base64,
                            prompt=prompt,
                            temperature=0.3
                        )

                        if description and "抱歉" not in description:
                            logger.info("使用硅基流动2多模态API生成上下文增强图像描述成功")
                            return description

                except Exception as e:
                    logger.warning(f"硅基流动2多模态API调用失败，回退到BLIP: {e}")

            # 回退到BLIP模型
            if self.blip_model is None:
                return "图像描述生成不可用"

            # 使用BLIP生成基础图像描述
            inputs = self.blip_processor(image, return_tensors="pt").to(self.device)

            with torch.no_grad():
                out = self.blip_model.generate(**inputs, max_length=50)
                description = self.blip_processor.decode(out[0], skip_special_tokens=True)

            # 如果有上下文信息，尝试增强描述
            if context_info:
                enhanced_description = self._enhance_description_with_context(description, context_info)
                if enhanced_description:
                    description = enhanced_description

            logger.info("使用BLIP模型生成图像描述")
            return description

        except Exception as e:
            logger.error(f"生成图像描述失败: {e}")
            return "图像描述生成失败"

    def generate_batch_image_descriptions(self, images_with_context: List[Dict[str, Any]],
                                        batch_size: int = 3) -> List[Dict[str, Any]]:
        """批量生成图像描述 - 优化版本，一次API调用处理多个图像

        Args:
            images_with_context: 图像和上下文信息列表，每个元素包含:
                - image: PIL图像对象
                - context_info: 上下文信息字典
                - image_id: 图像唯一标识
            batch_size: 每次API调用处理的图像数量

        Returns:
            List[Dict]: 包含图像描述结果的列表
        """
        try:
            if not images_with_context:
                return []

            logger.info(f"开始批量生成图像描述，总计 {len(images_with_context)} 张图像")

            all_results = []

            # 分批处理
            for i in range(0, len(images_with_context), batch_size):
                batch = images_with_context[i:i + batch_size]

                # 优先使用硅基流动2的批量API
                if self.config.SILICONFLOW2_API_KEY:
                    try:
                        batch_results = self._process_batch_with_siliconflow2(batch)
                        if batch_results:
                            all_results.extend(batch_results)
                            logger.info(f"成功处理批次 {i//batch_size + 1}，包含 {len(batch_results)} 张图像")
                            continue
                    except Exception as e:
                        logger.warning(f"硅基流动2批量处理失败，回退到单个处理: {e}")

                # 回退到单个处理
                for item in batch:
                    try:
                        description = self._generate_image_description(
                            item["image"],
                            item.get("context_info", {})
                        )
                        all_results.append({
                            "image_id": item.get("image_id", "unknown"),
                            "description": description,
                            "success": True,
                            "method": "single"
                        })
                    except Exception as e:
                        logger.error(f"单个图像处理失败: {e}")
                        all_results.append({
                            "image_id": item.get("image_id", "unknown"),
                            "description": f"图像处理失败: {str(e)}",
                            "success": False,
                            "error": str(e)
                        })

            success_count = sum(1 for r in all_results if r.get("success", False))
            logger.info(f"批量图像描述生成完成，成功 {success_count}/{len(all_results)} 张")

            return all_results

        except Exception as e:
            logger.error(f"批量生成图像描述失败: {e}")
            return [{"image_id": item.get("image_id", "unknown"),
                    "description": f"批量处理失败: {str(e)}",
                    "success": False,
                    "error": str(e)} for item in images_with_context]

    def _process_batch_with_siliconflow2(self, batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用硅基流动2批量处理图像"""
        try:
            # 准备批量数据
            images_data = []
            for item in batch:
                image = item["image"]
                context_info = item.get("context_info", {})

                # 将图像转换为base64
                image_base64 = self._image_to_base64(image)
                if not image_base64:
                    continue

                images_data.append({
                    "image_base64": image_base64,
                    "image_id": item.get("image_id", "unknown"),
                    "context_info": context_info
                })

            if not images_data:
                return []

            # 构建批量提示词
            prompt = self._build_batch_context_enhanced_prompt(images_data)

            # 调用批量API
            batch_result = self.llm_service.call_siliconflow2_vision_batch(
                images_data=images_data,
                prompt=prompt,
                temperature=0.3
            )

            if batch_result.get("success"):
                logger.info("使用硅基流动2批量多模态API生成上下文增强图像描述成功")
                return batch_result.get("results", [])
            else:
                logger.warning(f"硅基流动2批量API调用失败: {batch_result.get('error')}")
                return []

        except Exception as e:
            logger.error(f"硅基流动2批量处理失败: {e}")
            return []

    def _build_batch_context_enhanced_prompt(self, images_data: List[Dict[str, Any]]) -> str:
        """构建批量处理的上下文增强提示词"""
        try:
            base_prompt = f"""请详细分析这 {len(images_data)} 张图像的内容，特别关注以下方面：

1. 图像类型（如图表、表格、文档等）
2. 如果是图表，请识别图表类型（柱状图、折线图、饼图等）
3. 图像中的文字信息和数据
4. 图像的主要内容和要表达的信息
5. 如果涉及金融内容，请特别说明财务指标、趋势分析等

"""

            # 添加上下文信息
            context_added = False
            for i, img_data in enumerate(images_data):
                context_info = img_data.get("context_info", {})
                if context_info:
                    if not context_added:
                        base_prompt += "上下文信息：\n"
                        context_added = True

                    base_prompt += f"\n图像 {i+1} 的上下文：\n"

                    if context_info.get("page_text"):
                        page_text = context_info["page_text"][:300]  # 限制长度
                        base_prompt += f"  页面文本：{page_text}...\n"

                    if context_info.get("pdf_name"):
                        base_prompt += f"  来源文档：{context_info['pdf_name']}\n"

                    if context_info.get("page_number"):
                        base_prompt += f"  页码：第{context_info['page_number']}页\n"

            if context_added:
                base_prompt += "\n请结合上下文信息来增强图像描述的准确性和相关性。\n"

            # 添加输出格式要求
            base_prompt += f"""
请严格按照以下格式为每张图像生成描述：

图像1：[详细描述图像内容、类型、数据等]
图像2：[详细描述图像内容、类型、数据等]
...
图像{len(images_data)}：[详细描述图像内容、类型、数据等]

请确保：
- 每张图像都有对应的描述
- 按照图像顺序编号
- 描述要详细且专业
- 重点关注金融相关内容
"""

            return base_prompt

        except Exception as e:
            logger.warning(f"构建批量上下文增强提示词失败: {e}")
            return "请详细描述这些图像的内容，重点关注金融相关信息。"

    def _build_context_enhanced_prompt(self, context_info: Dict[str, str] = None) -> str:
        """构建包含上下文信息的提示词"""
        base_prompt = """请详细分析这张图像的内容，特别关注以下方面：
1. 图像类型（如图表、表格、文档等）
2. 如果是图表，请识别图表类型（柱状图、折线图、饼图等）
3. 图像中的文字信息和数据
4. 图像的主要内容和要表达的信息
5. 如果涉及金融内容，请特别说明"""

        if context_info:
            context_parts = []

            # 添加页面上下文
            if context_info.get("before_text"):
                context_parts.append(f"图像前文：{context_info['before_text'][:200]}")

            if context_info.get("after_text"):
                context_parts.append(f"图像后文：{context_info['after_text'][:200]}")

            if context_info.get("surrounding_text"):
                context_parts.append(f"周围文本：{context_info['surrounding_text'][:200]}")

            if context_parts:
                context_prompt = "\n\n参考上下文信息：\n" + "\n".join(context_parts)
                context_prompt += "\n\n请结合上下文信息分析图像内容，特别注意图像与周围文本的关联性。"
                base_prompt += context_prompt

        base_prompt += "\n\n请用中文回答，保持简洁明了。"
        return base_prompt

    def _enhance_description_with_context(self, description: str, context_info: Dict[str, str]) -> str:
        """使用上下文信息增强BLIP生成的描述"""
        try:
            if not context_info:
                return description

            enhanced_parts = [description]

            # 分析上下文中的关键信息
            context_keywords = self._extract_context_keywords(context_info)

            if context_keywords:
                # 添加上下文相关信息
                if any(keyword in context_keywords for keyword in ["财务", "金融", "报表", "收入", "利润", "资产"]):
                    enhanced_parts.append("该图像可能与财务报表相关")

                if any(keyword in context_keywords for keyword in ["图", "表", "数据", "统计"]):
                    enhanced_parts.append("该图像可能是数据图表")

                if any(keyword in context_keywords for keyword in ["年度", "季度", "月度", "期间"]):
                    enhanced_parts.append("该图像可能显示时间序列数据")

            # 添加位置上下文
            if context_info.get("before_text") or context_info.get("after_text"):
                enhanced_parts.append("图像位于相关文本内容中")

            return "，".join(enhanced_parts)

        except Exception as e:
            logger.warning(f"使用上下文增强描述失败: {e}")
            return description

    def _extract_context_keywords(self, context_info: Dict[str, str]) -> List[str]:
        """从上下文信息中提取关键词"""
        try:
            keywords = []

            # 金融相关关键词
            financial_keywords = [
                "财务", "金融", "报表", "收入", "利润", "资产", "负债", "现金流",
                "营业", "投资", "融资", "股东", "权益", "成本", "费用", "税收"
            ]

            # 图表相关关键词
            chart_keywords = [
                "图", "表", "数据", "统计", "分析", "趋势", "比较", "占比",
                "增长", "下降", "变化", "柱状", "折线", "饼图", "条形"
            ]

            # 时间相关关键词
            time_keywords = [
                "年度", "季度", "月度", "期间", "年", "月", "日", "时间",
                "历史", "当前", "未来", "预测", "同比", "环比"
            ]

            all_keywords = financial_keywords + chart_keywords + time_keywords

            # 检查所有上下文文本
            all_context = " ".join([
                context_info.get("page_text", ""),
                context_info.get("before_text", ""),
                context_info.get("after_text", ""),
                context_info.get("surrounding_text", "")
            ])

            for keyword in all_keywords:
                if keyword in all_context:
                    keywords.append(keyword)

            return keywords

        except Exception as e:
            logger.warning(f"提取上下文关键词失败: {e}")
            return []
    
    def _generate_image_embedding(self, image: Image.Image) -> Optional[np.ndarray]:
        """生成图像嵌入向量"""
        try:
            if self.clip_model is None:
                return None
            
            # 使用CLIP生成图像嵌入
            if hasattr(self, 'clip_preprocess'):
                # OpenAI CLIP
                image_input = self.clip_preprocess(image).unsqueeze(0).to(self.device)
                with torch.no_grad():
                    image_features = self.clip_model.encode_image(image_input)
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                return image_features.cpu().numpy().flatten()
            else:
                # HuggingFace CLIP
                inputs = self.clip_processor(images=image, return_tensors="pt").to(self.device)
                with torch.no_grad():
                    image_features = self.clip_model.get_image_features(**inputs)
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                return image_features.cpu().numpy().flatten()
                
        except Exception as e:
            logger.error(f"生成图像嵌入失败: {e}")
            return None
    
    def _classify_image_type(self, description: str, image: Image.Image, enhanced_type: str = None) -> str:
        """分类图像类型"""
        try:
            description_lower = description.lower()

            # 优先使用增强器检测的类型
            if enhanced_type and enhanced_type != "未知" and enhanced_type != "其他":
                return enhanced_type

            # 基于描述的关键词匹配
            for category, subcategories in self.financial_image_keywords.items():
                for subtype, keywords in subcategories.items():
                    if any(keyword in description_lower for keyword in keywords):
                        return subtype

            # 基于图像特征的简单分类
            width, height = image.size
            aspect_ratio = width / height

            # 根据长宽比推断可能的图表类型
            if 0.8 <= aspect_ratio <= 1.2:
                return "方形图表"
            elif aspect_ratio > 1.5:
                return "横向图表"
            elif aspect_ratio < 0.7:
                return "纵向图表"
            else:
                return "常规图像"
                
        except Exception as e:
            logger.error(f"分类图像类型失败: {e}")
            return "未知类型"
    
    def _extract_text_from_image(self, image: Image.Image) -> str:
        """从图像中提取文本（OCR）"""
        try:
            # 这里可以集成OCR功能，如pytesseract
            # 暂时返回空字符串
            return ""
        except Exception as e:
            logger.error(f"从图像提取文本失败: {e}")
            return ""

    def _extract_page_context_text(self, pdf_path: str, page_num: int, image_bbox: tuple = None) -> Dict[str, str]:
        """
        提取页面的上下文文本信息

        Args:
            pdf_path: PDF文件路径
            page_num: 页面编号（从0开始）
            image_bbox: 图像边界框 (x0, y0, x1, y1)，可选

        Returns:
            包含上下文文本的字典
        """
        try:
            import fitz  # PyMuPDF

            context_info = {
                "page_text": "",
                "surrounding_text": "",
                "before_text": "",
                "after_text": ""
            }

            # 使用PyMuPDF提取页面文本
            try:
                doc = fitz.open(pdf_path)
                if page_num < len(doc):
                    page = doc.load_page(page_num)

                    # 提取整页文本
                    page_text = page.get_text()
                    context_info["page_text"] = page_text.strip()

                    # 如果有图像边界框，尝试提取周围文本
                    if image_bbox and len(image_bbox) == 4:
                        try:
                            # 获取图像周围的文本块
                            text_blocks = page.get_text("dict")["blocks"]

                            img_x0, img_y0, img_x1, img_y1 = image_bbox

                            before_texts = []
                            after_texts = []
                            surrounding_texts = []

                            for block in text_blocks:
                                if "lines" in block:  # 文本块
                                    block_bbox = block["bbox"]
                                    block_y0, block_y1 = block_bbox[1], block_bbox[3]
                                    block_center_y = (block_y0 + block_y1) / 2

                                    # 提取块中的文本
                                    block_text = ""
                                    for line in block["lines"]:
                                        for span in line["spans"]:
                                            block_text += span["text"] + " "

                                    block_text = block_text.strip()
                                    if not block_text:
                                        continue

                                    # 判断文本块与图像的位置关系
                                    if block_center_y < img_y0 - 20:  # 图像上方
                                        before_texts.append(block_text)
                                    elif block_center_y > img_y1 + 20:  # 图像下方
                                        after_texts.append(block_text)
                                    elif (block_bbox[0] < img_x0 - 20 or block_bbox[2] > img_x1 + 20):  # 图像左右
                                        surrounding_texts.append(block_text)

                            # 组合上下文文本
                            context_info["before_text"] = " ".join(before_texts[-2:])  # 最近的2个文本块
                            context_info["after_text"] = " ".join(after_texts[:2])   # 最近的2个文本块
                            context_info["surrounding_text"] = " ".join(surrounding_texts)

                        except Exception as e:
                            logger.warning(f"提取图像周围文本失败: {e}")

                doc.close()

            except Exception as e:
                logger.warning(f"使用PyMuPDF提取页面文本失败: {e}")

            return context_info

        except Exception as e:
            logger.error(f"提取页面上下文文本失败: {e}")
            return {
                "page_text": "",
                "surrounding_text": "",
                "before_text": "",
                "after_text": ""
            }

    def index_images(self, pdf_path: str, file_hash: str) -> bool:
        """索引PDF中的图像"""
        try:
            # 提取图像
            images_info = self.extract_images_from_pdf(pdf_path, file_hash)

            if not images_info:
                logger.warning("未找到可索引的图像")
                return False

            # 保存到索引
            if file_hash not in self.image_index:
                self.image_index[file_hash] = {
                    "pdf_path": pdf_path,
                    "pdf_name": os.path.basename(pdf_path),
                    "images": [],
                    "total_images": 0
                }

            self.image_index[file_hash]["images"].extend(images_info)
            self.image_index[file_hash]["total_images"] = len(self.image_index[file_hash]["images"])

            # 保存索引
            self._save_image_index()

            logger.info(f"成功索引 {len(images_info)} 个图像")
            return True

        except Exception as e:
            logger.error(f"索引图像失败: {e}")
            return False

    def search_images_by_text(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """基于文本查询搜索图像"""
        try:
            if self.clip_model is None:
                logger.warning("CLIP模型未加载，无法进行图像搜索")
                return []

            # 生成查询文本的嵌入向量
            query_embedding = self._generate_text_embedding(query)
            if query_embedding is None:
                return []

            # 搜索相似图像（优化版本）
            results = []

            # 预先计算查询的金融关键词权重
            query_financial_weight = self._get_query_financial_weight(query)

            for file_hash, file_info in self.image_index.items():
                for image_info in file_info.get("images", []):
                    image_embedding = image_info.get("embedding")
                    if image_embedding is None:
                        continue

                    # 快速预筛选：先计算文本相似度
                    text_score = self._calculate_text_similarity(query, image_info)

                    # 如果文本相似度太低，跳过向量计算（提高速度）
                    if text_score < 0.1 and query_financial_weight < 0.3:
                        continue

                    # 计算余弦相似度
                    similarity = self._calculate_cosine_similarity(
                        query_embedding, np.array(image_embedding)
                    )

                    # 动态调整权重：金融相关查询更重视文本匹配
                    if query_financial_weight > 0.5:
                        final_score = similarity * 0.5 + text_score * 0.5
                    else:
                        final_score = similarity * 0.7 + text_score * 0.3

                    if final_score > 0.15:  # 提高相似度阈值
                        result = {
                            **image_info,
                            "pdf_name": file_info["pdf_name"],
                            "similarity": float(similarity),
                            "text_score": float(text_score),
                            "final_score": float(final_score)
                        }
                        results.append(result)

            # 按得分排序
            results.sort(key=lambda x: x["final_score"], reverse=True)

            return results[:top_k]

        except Exception as e:
            logger.error(f"基于文本搜索图像失败: {e}")
            return []

    def search_images_by_image(self, query_image_path: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """基于图像查询搜索相似图像"""
        try:
            if self.clip_model is None:
                logger.warning("CLIP模型未加载，无法进行图像搜索")
                return []

            # 加载查询图像
            query_image = Image.open(query_image_path)
            query_embedding = self._generate_image_embedding(query_image)

            if query_embedding is None:
                return []

            # 搜索相似图像
            results = []

            for file_hash, file_info in self.image_index.items():
                for image_info in file_info.get("images", []):
                    image_embedding = image_info.get("embedding")
                    if image_embedding is None:
                        continue

                    # 计算余弦相似度
                    similarity = self._calculate_cosine_similarity(
                        query_embedding, np.array(image_embedding)
                    )

                    if similarity > 0.2:  # 图像相似度阈值
                        result = {
                            **image_info,
                            "pdf_name": file_info["pdf_name"],
                            "similarity": float(similarity)
                        }
                        results.append(result)

            # 按相似度排序
            results.sort(key=lambda x: x["similarity"], reverse=True)

            return results[:top_k]

        except Exception as e:
            logger.error(f"基于图像搜索失败: {e}")
            return []

    def _generate_text_embedding(self, text: str) -> Optional[np.ndarray]:
        """生成文本嵌入向量"""
        try:
            if hasattr(self, 'clip_preprocess'):
                # OpenAI CLIP
                text_tokens = clip.tokenize([text]).to(self.device)
                with torch.no_grad():
                    text_features = self.clip_model.encode_text(text_tokens)
                    text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                return text_features.cpu().numpy().flatten()
            else:
                # HuggingFace CLIP
                inputs = self.clip_processor(text=[text], return_tensors="pt", padding=True).to(self.device)
                with torch.no_grad():
                    text_features = self.clip_model.get_text_features(**inputs)
                    text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                return text_features.cpu().numpy().flatten()

        except Exception as e:
            logger.error(f"生成文本嵌入失败: {e}")
            return None

    def _calculate_cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        try:
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0

    def _calculate_text_similarity(self, query: str, image_info: Dict[str, Any]) -> float:
        """计算增强的文本相似度"""
        try:
            query_lower = query.lower()
            query_words = set(query_lower.split())
            score = 0.0

            # 检查图像描述（权重最高）
            description = image_info.get("description", "").lower()
            description_words = set(description.split())
            desc_overlap = len(query_words.intersection(description_words))
            if desc_overlap > 0:
                score += 0.6 * (desc_overlap / len(query_words))

            # 检查图像类型
            image_type = image_info.get("image_type", "").lower()
            type_words = set(image_type.split())
            type_overlap = len(query_words.intersection(type_words))
            if type_overlap > 0:
                score += 0.4 * (type_overlap / len(query_words))

            # 检查提取的文本
            extracted_text = image_info.get("extracted_text", "").lower()
            text_words = set(extracted_text.split())
            text_overlap = len(query_words.intersection(text_words))
            if text_overlap > 0:
                score += 0.3 * (text_overlap / len(query_words))

            # 金融关键词匹配加分
            financial_score = self._calculate_financial_keyword_score(query_lower, image_info)
            score += financial_score

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"计算文本相似度失败: {e}")
            return 0.0

    def _calculate_financial_keyword_score(self, query: str, image_info: Dict[str, Any]) -> float:
        """计算金融关键词匹配得分"""
        try:
            score = 0.0
            description = image_info.get("description", "").lower()
            image_type = image_info.get("image_type", "").lower()

            # 遍历金融关键词
            for category, subcategories in self.financial_image_keywords.items():
                for subtype, keywords in subcategories.items():
                    # 检查查询中是否包含关键词
                    query_matches = any(keyword in query for keyword in keywords)
                    # 检查图片信息中是否包含关键词
                    desc_matches = any(keyword in description for keyword in keywords)
                    type_matches = any(keyword in image_type for keyword in keywords)

                    if query_matches and (desc_matches or type_matches):
                        # 根据类别给予不同权重
                        if category == "图表类型":
                            score += 0.3
                        elif category == "财务内容":
                            score += 0.4
                        elif category == "业务指标":
                            score += 0.2

            return min(score, 0.5)  # 最多加0.5分

        except Exception as e:
            logger.error(f"计算金融关键词得分失败: {e}")
            return 0.0

    def _get_query_financial_weight(self, query: str) -> float:
        """计算查询的金融相关权重"""
        try:
            query_lower = query.lower()
            weight = 0.0

            # 检查金融关键词
            for category, subcategories in self.financial_image_keywords.items():
                for subtype, keywords in subcategories.items():
                    if any(keyword in query_lower for keyword in keywords):
                        if category == "图表类型":
                            weight += 0.3
                        elif category == "财务内容":
                            weight += 0.4
                        elif category == "业务指标":
                            weight += 0.2

            # 检查通用金融词汇
            financial_terms = ["图表", "数据", "分析", "报表", "财务", "收入", "利润", "增长", "市场"]
            for term in financial_terms:
                if term in query_lower:
                    weight += 0.1

            return min(weight, 1.0)

        except Exception as e:
            logger.error(f"计算查询金融权重失败: {e}")
            return 0.0

    def get_image_statistics(self) -> Dict[str, Any]:
        """获取图像统计信息"""
        try:
            stats = {
                "total_files": len(self.image_index),
                "total_images": 0,
                "image_types": {},
                "size_distribution": {
                    "small": 0,    # < 300x300
                    "medium": 0,   # 300x300 - 800x800
                    "large": 0     # > 800x800
                },
                "pages_with_images": {}
            }

            for file_hash, file_info in self.image_index.items():
                images = file_info.get("images", [])
                stats["total_images"] += len(images)

                # 统计每个文件的图像页面分布
                pages = set()

                for image_info in images:
                    # 统计图像类型
                    image_type = image_info.get("image_type", "未知")
                    stats["image_types"][image_type] = stats["image_types"].get(image_type, 0) + 1

                    # 统计图像大小分布
                    width = image_info.get("width", 0)
                    height = image_info.get("height", 0)
                    size = max(width, height)

                    if size < 300:
                        stats["size_distribution"]["small"] += 1
                    elif size < 800:
                        stats["size_distribution"]["medium"] += 1
                    else:
                        stats["size_distribution"]["large"] += 1

                    # 记录包含图像的页面
                    pages.add(image_info.get("page_number", 0))

                stats["pages_with_images"][file_info["pdf_name"]] = len(pages)

            return stats

        except Exception as e:
            logger.error(f"获取图像统计信息失败: {e}")
            return {}

    def get_images_by_file(self, file_hash: str) -> List[Dict[str, Any]]:
        """根据文件哈希获取图像信息"""
        try:
            if file_hash in self.image_index:
                file_info = self.image_index[file_hash]
                return file_info.get("images", [])
            else:
                logger.warning(f"未找到文件哈希对应的图像索引: {file_hash}")
                return []

        except Exception as e:
            logger.error(f"获取文件图像信息失败: {e}")
            return []

    def analyze_image_content(self, image_id: str) -> Dict[str, Any]:
        """分析特定图像的内容"""
        try:
            # 查找图像信息
            image_info = None
            for file_info in self.image_index.values():
                for img in file_info.get("images", []):
                    if img.get("image_id") == image_id:
                        image_info = img
                        break
                if image_info:
                    break

            if not image_info:
                return {"error": "图像不存在"}

            # 加载图像
            image_path = image_info.get("image_path")
            if not os.path.exists(image_path):
                return {"error": "图像文件不存在"}

            image = Image.open(image_path)

            # 深度分析
            analysis = {
                "basic_info": {
                    "image_id": image_id,
                    "size": f"{image.width}x{image.height}",
                    "format": image.format,
                    "mode": image.mode
                },
                "description": image_info.get("description", ""),
                "image_type": image_info.get("image_type", ""),
                "color_analysis": ColorSpaceHandler.safe_analyze_colors(image),
                "complexity_score": ColorSpaceHandler.calculate_image_complexity(image),
                "financial_relevance": self._assess_financial_relevance(image_info)
            }

            return analysis

        except Exception as e:
            logger.error(f"分析图像内容失败: {e}")
            return {"error": str(e)}

    # 颜色分析和复杂度计算已移至 ColorSpaceHandler 工具类

    def _assess_financial_relevance(self, image_info: Dict[str, Any]) -> Dict[str, Any]:
        """评估图像的金融相关性"""
        try:
            relevance = {
                "score": 0.0,
                "categories": [],
                "confidence": "low"
            }

            description = image_info.get("description", "").lower()
            image_type = image_info.get("image_type", "").lower()

            # 检查金融关键词
            for category, subcategories in self.financial_image_keywords.items():
                for subtype, keywords in subcategories.items():
                    if any(keyword in description or keyword in image_type for keyword in keywords):
                        relevance["categories"].append(subtype)
                        relevance["score"] += 0.2

            # 设置置信度
            if relevance["score"] >= 0.6:
                relevance["confidence"] = "high"
            elif relevance["score"] >= 0.3:
                relevance["confidence"] = "medium"

            relevance["score"] = min(relevance["score"], 1.0)

            return relevance

        except Exception as e:
            logger.error(f"评估金融相关性失败: {e}")
            return {"score": 0.0, "categories": [], "confidence": "low"}
